#!/bin/bash

# 康复实习生出科成绩登记系统部署脚本

set -e

echo "🚀 开始部署康复实习生出科成绩登记系统..."

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose未安装，请先安装Docker Compose"
    exit 1
fi

# 创建必要的目录
echo "📁 创建必要的目录..."
mkdir -p logs uploads ssl

# 设置权限
chmod 755 logs uploads

# 停止现有服务
echo "🛑 停止现有服务..."
docker-compose down || true

# 构建镜像
echo "🔨 构建应用镜像..."
docker-compose build

# 启动服务
echo "🚀 启动服务..."
docker-compose up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 检查服务状态
echo "🔍 检查服务状态..."
docker-compose ps

# 健康检查
echo "🏥 进行健康检查..."
if curl -f http://localhost:3000/health > /dev/null 2>&1; then
    echo "✅ 应用服务健康检查通过"
else
    echo "❌ 应用服务健康检查失败"
    docker-compose logs app
    exit 1
fi

# 检查数据库连接
echo "🗄️ 检查数据库连接..."
if docker-compose exec mysql mysqladmin ping -h localhost --silent; then
    echo "✅ 数据库连接正常"
else
    echo "❌ 数据库连接失败"
    docker-compose logs mysql
    exit 1
fi

echo "🎉 部署完成！"
echo ""
echo "📋 服务信息："
echo "   应用地址: http://localhost:3000"
echo "   管理后台: http://localhost:3000/admin.html"
echo "   健康检查: http://localhost:3000/health"
echo "   数据库端口: 3306"
echo ""
echo "🔑 默认账号："
echo "   管理员: admin / 123456"
echo "   教学秘书: secretary01 / 123456"
echo "   带教老师: teacher01 / 123456"
echo ""
echo "⚠️  重要提醒："
echo "   1. 请及时修改默认密码"
echo "   2. 生产环境请配置HTTPS"
echo "   3. 定期备份数据库"
echo "   4. 监控系统日志"
echo ""
echo "📖 查看日志: docker-compose logs -f"
echo "🛑 停止服务: docker-compose down"
echo "🔄 重启服务: docker-compose restart"
