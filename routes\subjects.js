const express = require('express');
const router = express.Router();
const { execute } = require('../config/database');

// 获取考试科目列表
router.get('/', async (req, res) => {
    try {
        const [subjects] = await execute(
            'SELECT id, name, code, description, max_score, pass_score, weight FROM exam_subjects ORDER BY name'
        );
        
        res.json({
            success: true,
            message: '获取考试科目列表成功',
            data: subjects
        });
    } catch (error) {
        console.error('获取考试科目列表失败:', error);
        res.status(500).json({
            success: false,
            message: '获取考试科目列表失败'
        });
    }
});

module.exports = router;
