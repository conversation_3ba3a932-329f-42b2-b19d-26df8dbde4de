const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

const { testConnection } = require('./config/database');

const app = express();
const PORT = process.env.PORT || 3000;

// 安全中间件
app.use(helmet());

// 压缩中间件
app.use(compression());

// 日志中间件
app.use(morgan('combined'));

// CORS配置
app.use(cors({
    origin: process.env.NODE_ENV === 'production' 
        ? ['https://yourdomain.com'] 
        : ['http://localhost:3000', 'http://localhost:8080'],
    credentials: true
}));

// 请求体解析
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 速率限制
const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15分钟
    max: 100, // 限制每个IP 15分钟内最多100个请求
    message: {
        success: false,
        message: '请求过于频繁，请稍后再试'
    }
});
app.use('/api/', limiter);

// 静态文件服务
app.use('/uploads', express.static('uploads'));

// 健康检查
app.get('/health', (req, res) => {
    res.json({
        success: true,
        message: '服务运行正常',
        timestamp: new Date().toISOString()
    });
});

// API路由
app.use('/api/auth', require('./routes/auth'));
app.use('/api/students', require('./routes/students'));
app.use('/api/teachers', require('./routes/teachers'));
app.use('/api/exams', require('./routes/exams'));
app.use('/api/qr', require('./routes/qr'));
app.use('/api/schools', require('./routes/schools'));
app.use('/api/subjects', require('./routes/subjects'));

// 根路径
app.get('/', (req, res) => {
    res.json({
        success: true,
        message: '康复实习生出科成绩登记系统 API',
        version: '1.0.0',
        endpoints: {
            auth: '/api/auth',
            students: '/api/students',
            teachers: '/api/teachers',
            exams: '/api/exams',
            qr: '/api/qr',
            schools: '/api/schools',
            subjects: '/api/subjects'
        }
    });
});

// 404处理
app.use('*', (req, res) => {
    res.status(404).json({
        success: false,
        message: '接口不存在'
    });
});

// 全局错误处理
app.use((error, req, res, next) => {
    console.error('全局错误:', error);
    
    // 数据库错误
    if (error.code === 'ER_DUP_ENTRY') {
        return res.status(400).json({
            success: false,
            message: '数据已存在，请检查输入'
        });
    }
    
    // JWT错误
    if (error.name === 'JsonWebTokenError') {
        return res.status(401).json({
            success: false,
            message: '无效的访问令牌'
        });
    }
    
    // 验证错误
    if (error.name === 'ValidationError') {
        return res.status(400).json({
            success: false,
            message: '输入数据验证失败',
            errors: error.details
        });
    }
    
    // 默认错误
    res.status(500).json({
        success: false,
        message: process.env.NODE_ENV === 'production' 
            ? '服务器内部错误' 
            : error.message
    });
});

// 启动服务器
async function startServer() {
    try {
        // 测试数据库连接
        const dbConnected = await testConnection();
        if (!dbConnected) {
            console.warn('数据库连接失败，服务器将在演示模式下启动');
            console.warn('部分功能可能无法正常工作');
        }

        app.listen(PORT, () => {
            console.log(`服务器运行在端口 ${PORT}`);
            console.log(`API文档: http://localhost:${PORT}`);
            console.log(`健康检查: http://localhost:${PORT}/health`);
            console.log(`管理后台: http://localhost:${PORT}/admin.html`);
            if (!dbConnected) {
                console.log('⚠️  数据库未连接，请配置数据库后重启服务器');
            }
        });
    } catch (error) {
        console.error('服务器启动失败:', error);
        process.exit(1);
    }
}

// 优雅关闭
process.on('SIGTERM', () => {
    console.log('收到SIGTERM信号，正在关闭服务器...');
    process.exit(0);
});

process.on('SIGINT', () => {
    console.log('收到SIGINT信号，正在关闭服务器...');
    process.exit(0);
});

startServer();

// 导出app用于测试
module.exports = app;
