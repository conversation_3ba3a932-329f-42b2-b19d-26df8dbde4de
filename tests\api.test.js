const request = require('supertest');
const app = require('../server');

describe('API Tests', () => {
    let adminToken;
    let qrCode;

    // 测试健康检查
    test('GET /health should return 200', async () => {
        const response = await request(app)
            .get('/health')
            .expect(200);
        
        expect(response.body.success).toBe(true);
        expect(response.body.message).toBe('服务运行正常');
    });

    // 测试根路径
    test('GET / should return API info', async () => {
        const response = await request(app)
            .get('/')
            .expect(200);
        
        expect(response.body.success).toBe(true);
        expect(response.body.message).toBe('康复实习生出科成绩登记系统 API');
    });

    // 测试管理员登录（在没有数据库的情况下会失败，这是预期的）
    test('POST /api/auth/login should handle database connection error', async () => {
        const response = await request(app)
            .post('/api/auth/login')
            .send({
                username: 'admin',
                password: '123456'
            });

        // 在没有数据库的情况下，应该返回500错误
        expect(response.status).toBe(500);
        expect(response.body.success).toBe(false);
    });

    // 测试获取当前用户信息（没有token应该返回401）
    test('GET /api/auth/me should return 401 without token', async () => {
        const response = await request(app)
            .get('/api/auth/me');

        expect(response.status).toBe(401);
        expect(response.body.success).toBe(false);
    });

    // 测试获取学校列表（在没有数据库的情况下应该返回空数据）
    test('GET /api/schools should handle database connection error', async () => {
        const response = await request(app)
            .get('/api/schools');

        // 在没有数据库的情况下，应该返回成功但数据为空
        expect(response.body.success).toBe(true);
        expect(Array.isArray(response.body.data)).toBe(true);
    });

    // 测试获取考试科目列表
    test('GET /api/subjects should return subjects list', async () => {
        const response = await request(app)
            .get('/api/subjects')
            .expect(200);
        
        expect(response.body.success).toBe(true);
        expect(Array.isArray(response.body.data)).toBe(true);
    });

    // 测试生成二维码
    test('POST /api/qr/generate should create QR code', async () => {
        const response = await request(app)
            .post('/api/qr/generate')
            .set('Authorization', `Bearer ${adminToken}`)
            .send({
                type: 'student_register',
                title: '测试学生登记二维码',
                description: '用于测试的学生入科登记二维码',
                expires_hours: 24,
                max_usage: 10
            })
            .expect(200);
        
        expect(response.body.success).toBe(true);
        expect(response.body.data.qrCode).toBeDefined();
        qrCode = response.body.data.code;
    });

    // 测试获取二维码信息
    test('GET /api/qr/code/:code should return QR code info', async () => {
        const response = await request(app)
            .get(`/api/qr/code/${qrCode}`)
            .expect(200);
        
        expect(response.body.success).toBe(true);
        expect(response.body.data.type).toBe('student_register');
    });

    // 测试学生注册
    test('POST /api/students/register should register student', async () => {
        const response = await request(app)
            .post('/api/students/register')
            .send({
                username: 'teststudent',
                password: '123456',
                name: '测试学生',
                phone: '13900000000',
                email: '<EMAIL>',
                student_no: 'TEST001',
                gender: 'male',
                school_id: 1,
                major: '康复治疗学',
                grade: '2024',
                class_name: '测试班'
            })
            .expect(200);
        
        expect(response.body.success).toBe(true);
        expect(response.body.data.student).toBeDefined();
    });

    // 测试获取学生列表
    test('GET /api/students should return students list', async () => {
        const response = await request(app)
            .get('/api/students')
            .set('Authorization', `Bearer ${adminToken}`)
            .expect(200);
        
        expect(response.body.success).toBe(true);
        expect(response.body.data.students).toBeDefined();
    });

    // 测试404错误
    test('GET /api/nonexistent should return 404', async () => {
        const response = await request(app)
            .get('/api/nonexistent')
            .expect(404);
        
        expect(response.body.success).toBe(false);
        expect(response.body.message).toBe('接口不存在');
    });
});
