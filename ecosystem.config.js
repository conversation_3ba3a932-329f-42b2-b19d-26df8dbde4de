module.exports = {
  apps: [{
    name: 'rehabilitation-system',
    script: 'server.js',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'development',
      PORT: 3000
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }],

  deploy: {
    production: {
      user: 'node',
      host: 'your-server.com',
      ref: 'origin/main',
      repo: '**************:your-username/rehabilitation-system.git',
      path: '/var/www/rehabilitation-system',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && npm run init-db && pm2 reload ecosystem.config.js --env production',
      'pre-setup': ''
    }
  }
};
