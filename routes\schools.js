const express = require('express');
const router = express.Router();
const { execute } = require('../config/database');

// 获取学校列表
router.get('/', async (req, res) => {
    try {
        const [schools] = await execute(
            'SELECT id, name, code, address, contact_phone, contact_email FROM schools ORDER BY name'
        );
        
        res.json({
            success: true,
            message: '获取学校列表成功',
            data: schools
        });
    } catch (error) {
        console.error('获取学校列表失败:', error);
        res.status(500).json({
            success: false,
            message: '获取学校列表失败'
        });
    }
});

module.exports = router;
